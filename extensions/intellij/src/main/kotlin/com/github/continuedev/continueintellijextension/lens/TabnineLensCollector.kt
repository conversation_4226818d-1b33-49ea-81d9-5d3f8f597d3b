package com.tabnineCommon.chat.lens

import com.intellij.codeInsight.hints.FactoryInlayHintsCollector
import com.intellij.codeInsight.hints.InlayHintsSink
import com.intellij.codeInsight.hints.InlayPresentationFactory
import com.intellij.codeInsight.hints.presentation.InlayPresentation
import com.intellij.codeInsight.hints.presentation.SequencePresentation
import com.intellij.icons.AllIcons
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.ui.popup.JBPopup
import com.intellij.openapi.ui.popup.JBPopupFactory
import com.intellij.openapi.ui.popup.PopupStep
import com.intellij.openapi.ui.popup.util.BaseListPopupStep
import com.intellij.openapi.util.IconLoader
import com.intellij.openapi.util.TextRange
import com.intellij.psi.PsiElement
import com.intellij.psi.util.elementType
import com.intellij.refactoring.suggested.startOffset
import java.awt.Point
import java.awt.event.KeyEvent
import java.awt.event.MouseEvent

class TabnineLensCollector(
    editor: Editor,
    private val enabledElementTypes: List<String>,
    private val isChatEnabled: () -> Boolean
) : FactoryInlayHintsCollector(editor) {
    companion object {
        private const val ID = "com.tabnine.chat.lens"
    }

//    private val binaryRequestFacade = DependencyContainer.instanceOfBinaryRequestFacade()

    override fun collect(element: PsiElement, editor: Editor, sink: InlayHintsSink): Boolean {
//        if (!isChatEnabled()) {
//            return false
//        }
        if (element.elementType.toString() in enabledElementTypes) {
//            if (AppSettingsState.instance.interactionMode == 0) {
//                // 获取KeymapManager实例
//                val keymapManager = KeymapManager.getInstance()
//                // 获取当前活动的Keymap
//                val keymap = keymapManager.activeKeymap
//                val existingShortcut = keymap.getShortcuts(MyCustomAction.ACTION_ID)
//                var keyshort = ""
//                if (existingShortcut != null && existingShortcut.size > 0) {
//                    for (shortcut in existingShortcut) {
//                        if (shortcut is KeyboardShortcut) {
//                            keyshort =
//                                getShortcutText(shortcut.firstKeyStroke.keyCode, shortcut.firstKeyStroke.modifiers)
//                            break
//                        }
//                    }
//                }
//                sink.addBlockElement(
//                    offset = element.startOffset,
//                    relatesToPrecedingText = true,
//                    showAbove = true,
//                    priority = 0,
//                    presentation = factory.seq(
//                        factory.textSpacePlaceholder(countLeadingWhitespace(editor, element), false),
//                        factory.icon(StaticConfig.getTabnineLensIcon()),
//                        buildQuickActionItem("解释代码", "```/解释代码```", editor, element, false),
//                        buildQuickActionItem("生成单元测试", "```/生成单元测试```", editor, element, true),
//                        buildQuickActionItem("生成代码注释", "```/生成代码注释```", editor, element, true),
//                        buildQuickActionItem("生成优化建议", "```/生成优化建议```", editor, element, true),
//                        buildAskActionItem("代码问答", editor, element),
//                        factory.smallText("（代码补全快捷键：" + keyshort + "）")
//                    )
//                )
//            } else if (AppSettingsState.instance.interactionMode == 1) {
            val inlResult: InlResult =
                object : InlResult {
                    override fun onClick(editor: Editor, element: PsiElement, event: MouseEvent) {
                        if (editor.project != null) {
                            val popupActions: List<String> =
                                arrayListOf("解释代码", "生成单元测试", "生成代码注释", "生成优化建议", "代码问答")
                            val popup: JBPopup = JBPopupFactory.getInstance()
                                .createListPopup(object : BaseListPopupStep<String>("", popupActions) {
                                    override fun getTextFor(value: String): String {
                                        return value
                                    }

                                    override fun onChosen(
                                        selectedValue: String,
                                        finalChoice: Boolean
                                    ): PopupStep<*>? {
                                        var intent = when (selectedValue) {
                                            "解释代码" -> "```/解释代码```"
                                            "生成单元测试" -> "```/生成单元测试```"
                                            "生成代码注释" -> "```/生成代码注释```"
                                            "生成优化建议" -> "```/生成优化建议```"
                                            else -> "```/解释代码```"
                                        }

                                        if (selectedValue == "代码问答") {
                                            ApplicationManager.getApplication().invokeLater {
                                                sendClickEvent("ask")
                                                val result =
                                                    Messages.showInputDialog(
                                                        "对该段代码有什么想法么?",
                                                        "代码问答",
                                                        IconLoader.getIcon("/icons/continue.svg", javaClass)
                                                    )
                                                        .takeUnless { it.isNullOrBlank() }
                                                        ?: return@invokeLater

                                                selectElementRange(editor, element)
//                                                ChatActionCommunicator.sendMessageToChat(
//                                                    editor.project!!,
//                                                    ID,
//                                                    "```/$result```"
//                                                )
                                            }

                                            return FINAL_CHOICE
                                        }

                                        sendClickEvent(intent)

                                        selectElementRange(editor, element)
//                                        ChatActionCommunicator.sendMessageToChat(editor.project!!, ID, intent)
                                        return FINAL_CHOICE
                                    }
                                })
                            popup.showInScreenCoordinates(editor.component, event.locationOnScreen)
                        }
                    }

                    override val regularText: String
                        get() {
                            return ""
                        }
                }
            val presentations: ArrayList<InlayPresentation> = ArrayList()
            presentations.add(factory.textSpacePlaceholder(countLeadingWhitespace(editor, element), true))
            presentations.add(factory.icon(IconLoader.findIcon("/icons/continue.svg", javaClass.classLoader)!!))
            presentations.add(factory.icon(AllIcons.Actions.FindAndShowNextMatchesSmall))
            presentations.add(factory.textSpacePlaceholder(1, true))
            val shiftedPresentation = SequencePresentation(presentations)
            val finalPresentation = factory.referenceOnHover(
                shiftedPresentation,
                object : InlayPresentationFactory.ClickListener {
                    override fun onClick(event: MouseEvent, translated: Point) {
                        inlResult.onClick(editor, element, event)
                    }
                }
            )

            sink.addBlockElement(
                offset = element.startOffset,
                relatesToPrecedingText = true,
                showAbove = true,
                priority = 0,
                presentation = finalPresentation
            )
//            }
        }
        return true
    }

    private fun buildQuickActionItem(
        label: String,
        intent: String,
        editor: Editor,
        element: PsiElement,
        includeSeparator: Boolean
    ): InlayPresentation {
        return factory.seq(
            factory.smallText(" "),
            factory.smallText(if (includeSeparator) "| " else ""),
            factory.referenceOnHover(
                factory.smallText(label),
                object : InlayPresentationFactory.ClickListener {
                    override fun onClick(event: MouseEvent, translated: Point) {
                        sendClickEvent(intent)

                        selectElementRange(editor, element)
//                        ChatActionCommunicator.sendMessageToChat(editor.project!!, ID, intent)
                    }
                },
            )
        )
    }

    private fun buildAskActionItem(label: String, editor: Editor, element: PsiElement): InlayPresentation {
        return factory.seq(
            factory.smallText(" "),
            factory.smallText("| "),
            factory.referenceOnHover(
                factory.smallText(label),
                object : InlayPresentationFactory.ClickListener {
                    override fun onClick(event: MouseEvent, translated: Point) {
                        sendClickEvent("ask")

                        val result =
                            Messages.showInputDialog(
                                "对该段代码有什么想法么?",
                                "代码问答",
                                IconLoader.getIcon("/icons/continue.svg", javaClass)
                            )
                                .takeUnless { it.isNullOrBlank() }
                                ?: return

                        selectElementRange(editor, element)
//                        ChatActionCommunicator.sendMessageToChat(editor.project!!, ID, "```/$result```")
                    }
                },
            )
        )
    }

    private fun selectElementRange(editor: Editor, element: PsiElement) {
        val selectionModel = editor.selectionModel
        val range = element.textRange
        selectionModel.setSelection(range.startOffset, range.endOffset)
    }

    private fun sendClickEvent(intent: String) {
//        binaryRequestFacade.executeRequest(
//            EventRequest(
//                "chat-code-lens-click",
//                mapOf("intent" to intent)
//            )
//        )
    }

    private fun countLeadingWhitespace(editor: Editor, element: PsiElement): Int {
        val lineNumber = editor.document.getLineNumber(element.startOffset)
        return editor.document.getText(
            TextRange(
                editor.document.getLineStartOffset(lineNumber),
                editor.document.getLineEndOffset(lineNumber)
            )
        ).takeWhile { it.isWhitespace() }.length
    }

    interface InlResult {
        fun onClick(var1: Editor, var2: PsiElement, var3: MouseEvent)

        val regularText: String
    }

    private fun getShortcutText(keycode: Int, modifiers: Int): String {
        return KeyEvent.getModifiersExText(modifiers) + "+" + KeyEvent.getKeyText(keycode)
    }
}
