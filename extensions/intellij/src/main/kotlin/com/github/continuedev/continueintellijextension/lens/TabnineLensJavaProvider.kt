package com.github.continuedev.continueintellijextension.lens

import com.tabnineCommon.chat.lens.TabnineLensJavaBaseProvider
import com.tabnineCommon.chat.lens.TabnineLensKotlinBaseProvider
import com.tabnineCommon.chat.lens.TabnineLensPhpBaseProvider
import com.tabnineCommon.chat.lens.TabnineLensPythonBaseProvider
import com.tabnineCommon.chat.lens.TabnineLensRustBaseProvider
import com.tabnineCommon.chat.lens.TabnineLensTypescriptBaseProvider

open class TabnineLensJavaProvider : TabnineLensJavaBaseProvider({ true })
open class TabnineLensPythonProvider : TabnineLensPythonBaseProvider({ true })
open class TabnineLensTypescriptProvider : TabnineLensTypescriptBaseProvider({ true })
open class TabnineLensKotlinProvider : TabnineLensKotlinBaseProvider({ true })
open class TabnineLensPhpProvider : TabnineLensPhpBaseProvider({ true })
open class TabnineLensRustProvider : TabnineLensRustBaseProvider({ true })
