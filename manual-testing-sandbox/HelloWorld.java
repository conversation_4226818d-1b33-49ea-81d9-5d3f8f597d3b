import java.net.InetAddress;
import java.net.UnknownHostException;

// 定义一个公共类 HelloWorld
public class HelloWorld {
    // 主方法，程序的入口点
    public static void main(String[] args) {
        // 输出 "Hello, World!" 到控制台
        System.out.println("Hello, World!");

        // 输出当前时间到控制台
        System.out.println("Current time: " + getCurrentTime());

        // 输出本地 IP 地址到控制台
        System.out.println("Local IP address: " + getLocalIpAddress());
    }

    // 获取当前时间的方法
    public static String getCurrentTime() {
        // 获取当前时间
        java.time.LocalTime currentTime = java.time.LocalTime.now();
        // 打印调试信息到控制台
        System.out.println(1);
        // 返回当前时间的字符串表示
        return currentTime.toString();
    }

    // 获取本地 IP 地址的方法
    public static String getLocalIpAddress() {
        try {
            // 获取本地主机地址
            InetAddress localHost = InetAddress.getLocalHost();
            // 返回本地 IP 地址的字符串表示
            return "本地 IP 地址: " + localHost.getHostAddress();
        } catch (UnknownHostException e) {
            // 返回无法确定本地 IP 地址的消息
            return "无法确定本地 IP 地址";
        }
    }
}
